'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Swal from 'sweetalert2';
import { Tooltip } from 'react-tooltip';
import { showSuccessToast, showErrorToast } from '@/utils/function';

const SortableTreeItem = ({ item, onSelect, selectedId, onEdit, onDelete, onStatusToggle, children }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes}>
      <div className="mb-2">
        <div
          className={`flex items-center justify-between py-3 px-4 rounded-lg border cursor-pointer hover:border-primary-200 transition-base ${
            selectedId === item.id ? 'border-primary-500 bg-primary-50' : 'border-border-color hover:bg-gray-50'
          }`}
          onClick={() => onSelect(item)}
        >
          <div className="flex items-center gap-3 flex-1">
            {/* Drag Handle */}
            <div
              {...listeners}
              className="cursor-grab hover:cursor-grabbing p-1 text-gray-400 hover:text-gray-600"
              data-tooltip-id="drag-tooltip"
              data-tooltip-content="Drag to reorder"
            >
              <span className="icon icon-menu text-sm" />
            </div>

            {/* Category Icon */}
            {item.icon && item.icon.length > 0 && (
              <Image 
                src={item.icon[0].preview || item.icon[0]} 
                alt={item.name}
                className="w-8 h-8 object-cover rounded-lg"
                width={32}
                height={32}
                priority
              />
            )}

            {/* Category Info */}
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className={`text-sm font-medium ${!item.status ? 'text-gray-400' : 'text-gray-900'}`}>
                  {item.name}
                </h3>
                <span className={`w-2 h-2 rounded-full ${
                  item.status ? 'bg-success-500' : 'bg-gray-300'
                }`} />
              </div>
              {item.description && (
                <p className="text-xs text-gray-500 mt-1 line-clamp-1">
                  {item.description}
                </p>
              )}
              <div className="flex items-center gap-4 mt-1">
                <span className="text-xs text-gray-400">
                  ID: {item.id}
                </span>
                <span className="text-xs text-gray-400">
                  Slug: {item.slug}
                </span>
                {item.subcategories && (
                  <span className="text-xs text-gray-400">
                    Subcategories: {item.subcategories.length}
                  </span>
                )}
                {item.displayOnHomepage && (
                  <span className="text-xs bg-primary-100 text-primary-700 px-2 py-0.5 rounded">
                    Homepage
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1">
            <button
              type="button"
              className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-100 hover:text-primary-600 transition-base"
              onClick={(e) => {
                e.stopPropagation();
                onEdit(item, false);
              }}
              data-tooltip-id="edit-tooltip"
              data-tooltip-content="Edit category"
            >
              <span className="icon icon-edit text-sm" />
            </button>

            <button
              type="button"
              className={`w-8 h-8 flex items-center justify-center rounded-lg transition-base ${
                item.status 
                  ? 'hover:bg-warning-100 hover:text-warning-600' 
                  : 'hover:bg-success-100 hover:text-success-600'
              }`}
              onClick={(e) => {
                e.stopPropagation();
                onStatusToggle(item, false);
              }}
              data-tooltip-id="status-tooltip"
              data-tooltip-content={item.status ? 'Deactivate' : 'Activate'}
            >
              <span className={`icon ${item.status ? 'icon-eye-off' : 'icon-eye'} text-sm`} />
            </button>

            <button
              type="button"
              className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-danger-100 hover:text-danger-600 transition-base"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(item, false);
              }}
              data-tooltip-id="delete-tooltip"
              data-tooltip-content="Delete category"
            >
              <span className="icon icon-trash text-sm" />
            </button>
          </div>
        </div>

        {/* Subcategories */}
        {children}
      </div>
    </div>
  );
};

const SubcategoryItem = ({ item, onEdit, onDelete, onStatusToggle }) => {
  return (
    <div className="flex items-center justify-between py-2 px-3 ml-12 rounded-lg hover:bg-gray-50 transition-base">
      <div className="flex items-center gap-3 flex-1">
        {/* Subcategory Icon */}
        {item.icon && item.icon.length > 0 && (
          <Image 
            src={item.icon[0].preview || item.icon[0]} 
            alt={item.name}
            className="w-6 h-6 object-cover rounded"
            width={24}
            height={24}
            priority
          />
        )}

        {/* Subcategory Info */}
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <span className={`text-sm ${!item.status ? 'text-gray-400' : 'text-gray-700'}`}>
              {item.name}
            </span>
            <span className={`w-1.5 h-1.5 rounded-full ${
              item.status ? 'bg-success-500' : 'bg-gray-300'
            }`} />
          </div>
          {item.description && (
            <p className="text-xs text-gray-500 mt-0.5 line-clamp-1">
              {item.description}
            </p>
          )}
          <div className="flex items-center gap-3 mt-0.5">
            <span className="text-xs text-gray-400">
              ID: {item.id}
            </span>
            <span className="text-xs text-gray-400">
              Slug: {item.slug}
            </span>
          </div>
        </div>
      </div>

      {/* Subcategory Actions */}
      <div className="flex items-center gap-1">
        <button
          type="button"
          className="w-6 h-6 flex items-center justify-center rounded hover:bg-primary-100 hover:text-primary-600 transition-base"
          onClick={() => onEdit(item, true)}
          data-tooltip-id="edit-sub-tooltip"
          data-tooltip-content="Edit subcategory"
        >
          <span className="icon icon-edit text-xs" />
        </button>

        <button
          type="button"
          className={`w-6 h-6 flex items-center justify-center rounded transition-base ${
            item.status 
              ? 'hover:bg-warning-100 hover:text-warning-600' 
              : 'hover:bg-success-100 hover:text-success-600'
          }`}
          onClick={() => onStatusToggle(item, true)}
          data-tooltip-id="status-sub-tooltip"
          data-tooltip-content={item.status ? 'Deactivate' : 'Activate'}
        >
          <span className={`icon ${item.status ? 'icon-eye-off' : 'icon-eye'} text-xs`} />
        </button>

        <button
          type="button"
          className="w-6 h-6 flex items-center justify-center rounded hover:bg-danger-100 hover:text-danger-600 transition-base"
          onClick={() => onDelete(item, true)}
          data-tooltip-id="delete-sub-tooltip"
          data-tooltip-content="Delete subcategory"
        >
          <span className="icon icon-trash text-xs" />
        </button>
      </div>
    </div>
  );
};

const ServiceCategoriesTreeView = ({ data, onSelect, selectedId, onEdit, onDelete, onStatusToggle }) => {
  const [expandedCategories, setExpandedCategories] = useState(new Set());

  const toggleExpanded = (categoryId) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const handleDelete = async (item, isSubcategory) => {
    const itemType = isSubcategory ? 'subcategory' : 'category';
    const hasChildren = !isSubcategory && item.subcategories && item.subcategories.length > 0;
    
    if (hasChildren) {
      showErrorToast(`Cannot delete category with subcategories. Please delete subcategories first.`);
      return;
    }

    const result = await Swal.fire({
      title: `Delete ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`,
      html: `
        <div class="text-left">
          <p class="mb-3 text-sm text-center text-gray-600">Are you sure you want to delete this ${itemType}? This action cannot be undone.</p>
          <div class="bg-gray-50 p-3 rounded-lg border">
            <div class="flex items-center gap-3 mb-2">
              ${item.icon && item.icon.length > 0 ? 
                `<img src="${item.icon[0].preview || item.icon[0]}" alt="${item.name}" class="w-8 h-8 rounded object-cover" />` : 
                '<div class="w-8 h-8 bg-gray-200 rounded flex items-center justify-center"><span class="icon icon-folder text-gray-400 text-sm"></span></div>'
              }
              <div>
                <p class="text-sm font-medium text-gray-900">${item.name}</p>
                <p class="text-xs text-gray-500">${item.slug}</p>
              </div>
            </div>
            ${item.description ? `<p class="text-xs text-gray-600">${item.description}</p>` : ''}
          </div>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#db3545',
      cancelButtonColor: '#b3b6b5',
      confirmButtonText: `Yes, Delete ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`,
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2 !font-medium',
        cancelButton: '!rounded-lg px-4 py-2 !font-medium'
      }
    });

    if (result.isConfirmed) {
      onDelete(item, isSubcategory);
      
      Swal.fire({
        title: 'Deleted!',
        text: `${itemType.charAt(0).toUpperCase() + itemType.slice(1)} has been deleted successfully.`,
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-2xl'
        }
      });
    }
  };

  const handleStatusToggle = async (item, isSubcategory) => {
    const itemType = isSubcategory ? 'subcategory' : 'category';
    const newStatus = !item.status;
    const action = newStatus ? 'activate' : 'deactivate';

    const result = await Swal.fire({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`,
      text: `Are you sure you want to ${action} "${item.name}"?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: newStatus ? '#10b857' : '#f59e0b',
      cancelButtonColor: '#6B7280',
      confirmButtonText: `Yes, ${action.charAt(0).toUpperCase() + action.slice(1)}`,
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2',
        cancelButton: '!rounded-lg px-4 py-2'
      }
    });

    if (result.isConfirmed) {
      onStatusToggle(item, isSubcategory);
      showSuccessToast(`${itemType.charAt(0).toUpperCase() + itemType.slice(1)} ${action}d successfully`);
    }
  };

  return (
    <div className="space-y-2">
      {data.map((category) => {
        const hasSubcategories = category.subcategories && category.subcategories.length > 0;
        const isExpanded = expandedCategories.has(category.id);

        return (
          <SortableTreeItem
            key={category.id}
            item={category}
            onSelect={onSelect}
            selectedId={selectedId}
            onEdit={onEdit}
            onDelete={(item, isSubcategory) => handleDelete(item, isSubcategory)}
            onStatusToggle={(item, isSubcategory) => handleStatusToggle(item, isSubcategory)}
          >
            {hasSubcategories && (
              <div className="mt-2">
                <button
                  type="button"
                  className="flex items-center gap-2 ml-12 text-xs text-gray-500 hover:text-primary-600 transition-base"
                  onClick={() => toggleExpanded(category.id)}
                >
                  <span className={`icon ${isExpanded ? 'icon-chevron-down' : 'icon-chevron-right'} text-xs`} />
                  {category.subcategories.length} subcategories
                </button>
                
                {isExpanded && (
                  <div className="mt-2 space-y-1">
                    {category.subcategories.map((subcategory) => (
                      <SubcategoryItem
                        key={subcategory.id}
                        item={subcategory}
                        onEdit={onEdit}
                        onDelete={(item, isSubcategory) => handleDelete(item, isSubcategory)}
                        onStatusToggle={(item, isSubcategory) => handleStatusToggle(item, isSubcategory)}
                      />
                    ))}
                  </div>
                )}
              </div>
            )}
          </SortableTreeItem>
        );
      })}

      {/* Tooltips */}
      <Tooltip id="drag-tooltip" />
      <Tooltip id="edit-tooltip" />
      <Tooltip id="status-tooltip" />
      <Tooltip id="delete-tooltip" />
      <Tooltip id="edit-sub-tooltip" />
      <Tooltip id="status-sub-tooltip" />
      <Tooltip id="delete-sub-tooltip" />
    </div>
  );
};

export default ServiceCategoriesTreeView;
