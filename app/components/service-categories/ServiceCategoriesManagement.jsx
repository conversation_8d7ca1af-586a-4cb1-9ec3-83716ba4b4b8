'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { showSuccessToast, showErrorToast } from '@/utils/function';
import FilterField from '../table/FilterField';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import ToggleSwitch from '../Inputs/ToggleSwitch';
import ImageUpload from '../Inputs/ImageUpload';
import ServiceCategoriesTreeView from './ServiceCategoriesTreeView';

// Utility functions for service category management
const generateSlug = (name) => {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

const validateSlug = (slug) => {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
};

const ServiceCategoriesManagement = () => {
  // State management
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formType, setFormType] = useState('category'); // 'category' or 'subcategory'
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const displayMenuRef = useRef(null);

  // Sample data for development
  useEffect(() => {
    const sampleCategories = [
      {
        id: 1,
        name: 'Home Services',
        slug: 'home-services',
        description: 'Professional home maintenance and repair services',
        icon: [],
        image: [],
        parentCategory: null,
        displayOnHomepage: true,
        sortOrder: 0,
        status: true,
        createdAt: new Date().toISOString(),
        createdBy: 'Admin',
        subcategories: [
          {
            id: 11,
            name: 'Plumbing',
            slug: 'plumbing',
            description: 'Professional plumbing services',
            icon: [],
            status: true,
            createdAt: new Date().toISOString()
          },
          {
            id: 12,
            name: 'Electrical',
            slug: 'electrical',
            description: 'Electrical installation and repair',
            icon: [],
            status: true,
            createdAt: new Date().toISOString()
          }
        ]
      },
      {
        id: 2,
        name: 'Business Services',
        slug: 'business-services',
        description: 'Professional business and consulting services',
        icon: [],
        image: [],
        parentCategory: null,
        displayOnHomepage: false,
        sortOrder: 1,
        status: true,
        createdAt: new Date().toISOString(),
        createdBy: 'Admin',
        subcategories: []
      }
    ];
    setCategories(sampleCategories);
  }, []);

  // Validation schema
  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .required('Name is required')
      .max(100, 'Name must be less than 100 characters')
      .test('unique-name', 'Name already exists', function(value) {
        if (!value) return true;
        const { parent } = this;
        const currentId = parent.id;
        
        if (formType === 'category') {
          return !categories.some(cat => 
            cat.id !== currentId && 
            cat.name.toLowerCase() === value.toLowerCase()
          );
        } else {
          if (!selectedCategory) return true;
          return !selectedCategory.subcategories.some(sub => 
            sub.id !== currentId && 
            sub.name.toLowerCase() === value.toLowerCase()
          );
        }
      }),
    slug: Yup.string()
      .test('valid-slug', 'Slug must be URL-friendly (lowercase letters, numbers, and hyphens only)', function(value) {
        if (!value) return true;
        return validateSlug(value);
      })
      .test('unique-slug', 'Slug already exists', function(value) {
        if (!value) return true;
        const { parent } = this;
        const currentId = parent.id;
        const slug = value || generateSlug(parent.name);

        if (formType === 'category') {
          return !categories.some(cat =>
            cat.id !== currentId &&
            cat.slug === slug
          );
        } else {
          if (!selectedCategory) return true;
          return !selectedCategory.subcategories.some(sub =>
            sub.id !== currentId &&
            sub.slug === slug
          );
        }
      }),
    description: Yup.string().max(500, 'Description must be less than 500 characters'),
    sortOrder: Yup.number().min(0, 'Sort order must be a positive number')
  });

  // Get initial form values
  const getInitialValues = () => {
    if (editingCategory) {
      return {
        id: editingCategory.id,
        name: editingCategory.name || '',
        slug: editingCategory.slug || '',
        description: editingCategory.description || '',
        parentCategory: editingCategory.parentCategory || (formType === 'subcategory' ? selectedCategory?.id : ''),
        icon: editingCategory.icon || [],
        image: editingCategory.image || [],
        displayOnHomepage: editingCategory.displayOnHomepage || false,
        sortOrder: editingCategory.sortOrder || 0,
        status: editingCategory.status !== undefined ? editingCategory.status : true
      };
    }
    
    return {
      name: '',
      slug: '',
      description: '',
      parentCategory: formType === 'subcategory' ? selectedCategory?.id || '' : '',
      icon: [],
      image: [],
      displayOnHomepage: false,
      sortOrder: formType === 'category' ? categories.length : (selectedCategory?.subcategories?.length || 0),
      status: true
    };
  };

  // Get parent category options for dropdown
  const getParentCategoryOptions = () => {
    return categories.map(cat => ({
      value: cat.id,
      label: cat.name
    }));
  };

  // Filter categories based on search text
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(filterText.toLowerCase()) ||
    category.description?.toLowerCase().includes(filterText.toLowerCase()) ||
    category.subcategories.some(sub => 
      sub.name.toLowerCase().includes(filterText.toLowerCase()) ||
      sub.description?.toLowerCase().includes(filterText.toLowerCase())
    )
  );

  // Form submission handler
  const handleFormSubmit = async (values) => {
    try {
      const slug = values.slug || generateSlug(values.name);
      
      if (formType === 'category') {
        if (editingCategory) {
          // Update existing category
          const updatedCategories = categories.map(cat => {
            if (cat.id === editingCategory.id) {
              return {
                ...cat,
                ...values,
                slug,
                updatedAt: new Date().toISOString()
              };
            }
            return cat;
          });
          setCategories(updatedCategories);
          showSuccessToast('Service category updated successfully');
        } else {
          // Create new category
          const newCategory = {
            id: Date.now(),
            ...values,
            slug,
            subcategories: [],
            sortOrder: categories.length,
            createdAt: new Date().toISOString(),
            createdBy: 'Admin'
          };
          setCategories([...categories, newCategory]);
          showSuccessToast('Service category created successfully');
        }
      } else {
        if (!selectedCategory) {
          showErrorToast('Please select a parent category first');
          return;
        }

        if (editingCategory) {
          // Update existing subcategory
          const updatedCategories = categories.map(cat => {
            if (cat.id === selectedCategory.id) {
              return {
                ...cat,
                subcategories: cat.subcategories.map(sub => {
                  if (sub.id === editingCategory.id) {
                    return {
                      ...sub,
                      ...values,
                      slug,
                      updatedAt: new Date().toISOString()
                    };
                  }
                  return sub;
                })
              };
            }
            return cat;
          });
          setCategories(updatedCategories);
          showSuccessToast('Service subcategory updated successfully');
        } else {
          // Create new subcategory
          const newSubcategory = {
            id: Date.now(),
            ...values,
            slug,
            sortOrder: selectedCategory.subcategories.length,
            createdAt: new Date().toISOString()
          };

          const updatedCategories = categories.map(cat => {
            if (cat.id === selectedCategory.id) {
              return {
                ...cat,
                subcategories: [...cat.subcategories, newSubcategory]
              };
            }
            return cat;
          });
          setCategories(updatedCategories);
          showSuccessToast('Service subcategory created successfully');
        }
      }

      setIsDrawerOpen(false);
      setEditingCategory(null);
    } catch (error) {
      showErrorToast('An error occurred while saving');
      console.error('Form submission error:', error);
    }
  };

  // Delete handler
  const handleDelete = (item, isSubcategory) => {
    if (isSubcategory) {
      // Delete subcategory
      const updatedCategories = categories.map(cat => {
        if (cat.subcategories.some(sub => sub.id === item.id)) {
          return {
            ...cat,
            subcategories: cat.subcategories.filter(sub => sub.id !== item.id)
          };
        }
        return cat;
      });
      setCategories(updatedCategories);
    } else {
      // Delete category
      setCategories(categories.filter(cat => cat.id !== item.id));
      // Clear selection if deleted category was selected
      if (selectedCategory?.id === item.id) {
        setSelectedCategory(null);
      }
    }
  };

  // Status toggle handler
  const handleStatusToggle = (item, isSubcategory) => {
    if (isSubcategory) {
      // Toggle subcategory status
      const updatedCategories = categories.map(cat => {
        if (cat.subcategories.some(sub => sub.id === item.id)) {
          return {
            ...cat,
            subcategories: cat.subcategories.map(sub => {
              if (sub.id === item.id) {
                return { ...sub, status: !sub.status, updatedAt: new Date().toISOString() };
              }
              return sub;
            })
          };
        }
        return cat;
      });
      setCategories(updatedCategories);
    } else {
      // Toggle category status
      const updatedCategories = categories.map(cat => {
        if (cat.id === item.id) {
          const newStatus = !cat.status;
          return {
            ...cat,
            status: newStatus,
            updatedAt: new Date().toISOString(),
            // If deactivating category, also deactivate subcategories
            subcategories: newStatus ? cat.subcategories : cat.subcategories.map(sub => ({
              ...sub,
              status: false,
              updatedAt: new Date().toISOString()
            }))
          };
        }
        return cat;
      });
      setCategories(updatedCategories);
    }
  };

  // Drag end handler for reordering
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = categories.findIndex(cat => cat.id === active.id);
    const newIndex = categories.findIndex(cat => cat.id === over.id);

    if (oldIndex !== -1 && newIndex !== -1) {
      const newCategories = [...categories];
      const [reorderedItem] = newCategories.splice(oldIndex, 1);
      newCategories.splice(newIndex, 0, reorderedItem);

      // Update sort order
      const updatedCategories = newCategories.map((cat, index) => ({
        ...cat,
        sortOrder: index,
        updatedAt: new Date().toISOString()
      }));

      setCategories(updatedCategories);
      showSuccessToast('Categories reordered successfully');
    }
  };

  return (
    <div className="bg-white rounded-xl">
      {/* Filter and Search */}
      <FilterField
        filterText={filterText}
        setFilterText={setFilterText}
        isSearchFilterOpen={isSearchFilterOpen}
        setIsSearchFilterOpen={setIsSearchFilterOpen}
        isDisplayMenuOpen={isDisplayMenuOpen}
        setIsDisplayMenuOpen={setIsDisplayMenuOpen}
        displayMenuRef={displayMenuRef}
        placeholder="Search service categories..."
      >
        <div className="flex items-center gap-2">
          <button
            type="button"
            className="btn btn-primary flex items-center gap-2"
            onClick={() => {
              setFormType('category');
              setEditingCategory(null);
              setIsDrawerOpen(true);
            }}
          >
            <span className="icon icon-plus text-base" />
            Add Category
          </button>
          
          {selectedCategory && (
            <button
              type="button"
              className="btn btn-outline-primary flex items-center gap-2"
              onClick={() => {
                setFormType('subcategory');
                setEditingCategory(null);
                setIsDrawerOpen(true);
              }}
            >
              <span className="icon icon-plus text-base" />
              Add Subcategory
            </button>
          )}

          <div className="flex items-center gap-2">
            <button
              type="button"
              className="btn btn-outline-gray flex items-center gap-2"
              onClick={() => {
                // TODO: Implement CSV export
                showSuccessToast('CSV export feature coming soon');
              }}
            >
              <span className="icon icon-download text-base" />
              Export CSV
            </button>

            <button
              type="button"
              className="btn btn-outline-gray flex items-center gap-2"
              onClick={() => {
                // TODO: Implement CSV import
                showSuccessToast('CSV import feature coming soon');
              }}
            >
              <span className="icon icon-upload text-base" />
              Import CSV
            </button>
          </div>
        </div>
      </FilterField>

      {/* Tree View */}
      <div className="p-4">
        <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext items={filteredCategories.map(cat => cat.id)} strategy={verticalListSortingStrategy}>
            <ServiceCategoriesTreeView
              data={filteredCategories}
              onSelect={setSelectedCategory}
              selectedId={selectedCategory?.id}
              onEdit={(item, isSubcategory) => {
                setEditingCategory(item);
                setFormType(isSubcategory ? 'subcategory' : 'category');
                setIsDrawerOpen(true);
              }}
              onDelete={handleDelete}
              onStatusToggle={handleStatusToggle}
            />
          </SortableContext>
        </DndContext>
      </div>

      {/* Form Modal */}
      <BaseOffCanvas
        isOpen={isDrawerOpen}
        onClose={() => {
          setIsDrawerOpen(false);
          setEditingCategory(null);
        }}
        size="sm"
        title={`${editingCategory ? 'Edit' : 'Add'} ${formType === 'category' ? 'Service Category' : 'Service Subcategory'}`}
      >
        <Formik
          initialValues={getInitialValues()}
          validationSchema={validationSchema}
          onSubmit={handleFormSubmit}
          enableReinitialize
        >
          {({ values, errors, touched, setFieldValue, handleChange }) => (
            <Form>
              <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
                <InputField
                  id="name"
                  type="text"
                  label={formType === 'category' ? 'Category Name' : 'Subcategory Name'}
                  name="name"
                  value={values.name}
                  onChange={(e) => {
                    handleChange(e);
                    // Auto-generate slug if slug field is empty
                    if (!values.slug) {
                      setFieldValue('slug', generateSlug(e.target.value));
                    }
                  }}
                  error={touched.name && errors.name}
                  required
                  maxLength={100}
                  formik={{ values, errors, touched, setFieldValue }}
                />

                <InputField
                  id="slug"
                  type="text"
                  label="Slug / Code"
                  name="slug"
                  value={values.slug}
                  onChange={handleChange}
                  error={touched.slug && errors.slug}
                  placeholder="auto-generated-from-name"
                  formik={{ values, errors, touched, setFieldValue }}
                />

                {formType === 'category' && (
                  <div className="flex flex-col flex-1 mb-4">
                    <span className="form-label">Parent Category</span>
                    <SelectField
                      name="parentCategory"
                      placeholder="Select parent category (optional)"
                      options={getParentCategoryOptions()}
                      value={values.parentCategory}
                      onChange={(selectedOption) => {
                        setFieldValue('parentCategory', selectedOption?.value || '');
                      }}
                      isClearable
                      formik={{ values, errors, touched, setFieldValue }}
                    />
                  </div>
                )}

                <div className="flex flex-col flex-1 mb-4">
                  <span className="form-label">Description</span>
                  <textarea
                    name="description"
                    value={values.description}
                    onChange={handleChange}
                    placeholder="Brief category overview"
                    className="form-control min-h-[80px]"
                    maxLength={500}
                  />
                  {touched.description && errors.description && (
                    <span className="block text-danger-500 text-xs mt-1">
                      {errors.description}
                    </span>
                  )}
                </div>

                <div className="flex flex-col flex-1 mb-4">
                  <span className="form-label">Category Icon</span>
                  <ImageUpload
                    name="icon"
                    setImages={(images) => setFieldValue('icon', images)}
                    maxFiles={1}
                    maxSize={2000000} // 2MB
                    uploadText="Upload Icon"
                    tooltipContent="Upload an icon (64x64 px recommended, PNG/SVG)"
                    showFileInfo={false}
                  />
                </div>

                {formType === 'category' && (
                  <div className="flex flex-col flex-1 mb-4">
                    <span className="form-label">Category Image</span>
                    <ImageUpload
                      name="image"
                      setImages={(images) => setFieldValue('image', images)}
                      maxFiles={1}
                      maxSize={5000000} // 5MB
                      uploadText="Upload Image"
                      tooltipContent="Upload a banner image (1:1 or 4:3 aspect ratio recommended)"
                      showFileInfo={false}
                    />
                  </div>
                )}

                {formType === 'category' && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">
                      Display on Homepage
                    </label>
                    <ToggleSwitch
                      checked={values.displayOnHomepage}
                      onChange={(e) => setFieldValue('displayOnHomepage', e.target.checked)}
                    />
                    <span className="text-xs text-gray-500 mt-1 block">
                      Enable to feature this category on the homepage
                    </span>
                  </div>
                )}

                <InputField
                  id="sortOrder"
                  type="number"
                  label="Sort Order"
                  name="sortOrder"
                  value={values.sortOrder}
                  onChange={handleChange}
                  error={touched.sortOrder && errors.sortOrder}
                  placeholder="0"
                  formik={{ values, errors, touched, setFieldValue }}
                />

                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">
                    Status
                  </label>
                  <ToggleSwitch
                    checked={values.status}
                    onChange={(e) => setFieldValue('status', e.target.checked)}
                  />
                  <span className="text-xs text-gray-500 mt-1 block">
                    {values.status ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
                <button
                  type="button"
                  className="btn btn-outline-gray"
                  onClick={() => {
                    setIsDrawerOpen(false);
                    setEditingCategory(null);
                  }}
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingCategory ? 'Update' : 'Save'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </BaseOffCanvas>
    </div>
  );
};

export default ServiceCategoriesManagement;
